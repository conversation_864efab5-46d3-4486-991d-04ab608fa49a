package ai.conrain.aigc.platform.service.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class JsonMerger {

    /**
     * 深度合并两个 JSONObject
     * - 对象：递归合并
     * - 数组：去重合并（union）
     * - 基本类型：右侧覆盖左侧
     *
     * @param a 左侧对象
     * @param b 右侧对象（优先级高）
     * @return 合并后的新对象
     */
    public static JSONObject merge(JSONObject a, JSONObject b) {
        if (b == null) {
            return a;
        }
        if (a == null) {
            a = new JSONObject();
        }
        JSONObject result = new JSONObject();

        // 1. 添加 a 中所有字段
        for (Map.Entry<String, Object> entry : a.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            result.put(key, deepCopy(value));
        }

        // 2. 合并 b 中字段（覆盖或合并）
        for (Map.Entry<String, Object> entry : b.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            Object existing = result.get(key);

            result.put(key, mergeValue(existing, value));
        }

        return result;
    }

    /**
     * 合并两个任意值
     */
    private static Object mergeValue(Object a, Object b) {
        // b 为 null，返回 a
        if (b == null) {
            return a;
        }

        // a 不存在，直接返回 b
        if (a == null) {
            return deepCopy(b);
        }

        // 类型不同，b 覆盖 a
        if (!sameType(a, b)) {
            return deepCopy(b);
        }

        // 都是对象
        if (a instanceof JSONObject && b instanceof JSONObject) {
            return merge((JSONObject) a, (JSONObject) b);
        }

        // 都是数组
        if (a instanceof Collection && b instanceof Collection) {
            return unionArrays(a, b);
        }

        // 其他类型（字符串、数字等），b 覆盖 a
        return deepCopy(b);
    }

    /**
     * 数组合并：去重，保持 a 的顺序 + b 中新增的
     */
    private static List<Object> unionArrays(Object a, Object b) {
        Set<String> seen = new LinkedHashSet<>();
        List<Object> result = new ArrayList<>();

        // 添加 a 中元素
        toList(a).forEach(item -> {
            String key = valueKey(item);
            if (!key.isEmpty() && seen.add(key)) {
                result.add(item);
            }
        });

        // 添加 b 中新元素
        toList(b).forEach(item -> {
            String key = valueKey(item);
            if (!key.isEmpty() && seen.add(key)) {
                result.add(item);
            }
        });

        return result;
    }

    /**
     * 安全转为 List
     */
    private static List<Object> toList(Object obj) {
        if (obj instanceof Collection) {
            return new ArrayList<>((Collection<?>) obj);
        } else {
            return Collections.singletonList(obj);
        }
    }

    /**
     * 为去重生成唯一 key（主要是字符串化）
     */
    private static String valueKey(Object obj) {
        if (obj == null) return "";
        return obj.toString().trim();
    }

    /**
     * 判断是否同类型（简化版）
     */
    private static boolean sameType(Object a, Object b) {
        if (a instanceof Map<?,?> && b instanceof Map<?,?>) return true;
        if (a instanceof Collection && b instanceof Collection) return true;
        return a.getClass().equals(b.getClass()) ||
               (a instanceof Number && b instanceof Number);
    }

    /**
     * 深拷贝任意对象（Fastjson2 方式）
     */
    private static Object deepCopy(Object obj) {
        if (obj == null) return null;
        return JSON.parse(JSON.toJSONString(obj));
    }

    // -------------------- 便捷方法 --------------------

    /**
     * 合并多个对象
     */
    public static JSONObject mergeAll(JSONObject... objects) {
        JSONObject result = new JSONObject();
        for (JSONObject obj : objects) {
            if (obj != null) {
                result = merge(result, obj);
            }
        }
        return result;
    }
}