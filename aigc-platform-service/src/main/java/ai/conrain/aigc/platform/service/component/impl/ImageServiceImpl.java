package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.MaterialInfoDAO;
import ai.conrain.aigc.platform.dal.entity.MaterialInfoDO;
import ai.conrain.aigc.platform.dal.example.ImageExample;
import ai.conrain.aigc.platform.dal.example.MaterialInfoExample;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageCaptionUserDAO;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageDO;
import ai.conrain.aigc.platform.dal.pgsql.param.ImageBatchCountParam;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import ai.conrain.aigc.platform.service.enums.ImageTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.ImageConverter;
import ai.conrain.aigc.platform.service.model.converter.MaterialInfoConverter;
import ai.conrain.aigc.platform.service.model.query.ImageQuery;
import ai.conrain.aigc.platform.service.model.query.MaterialInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.ClothInfoVO;
import ai.conrain.aigc.platform.service.model.vo.ImageUploadReq;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ImageInfoUtil;
import ai.conrain.aigc.platform.service.util.ImageInfoUtil.ImageInfo;
import ai.conrain.aigc.platform.service.util.JsonMerger;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Semaphore;
import org.springframework.transaction.annotation.Transactional;

/**
 * ImageService实现
 *
 * <AUTHOR>
 * @version ImageService.java v 0.1 2025-07-30 02:33:24
 */
@Slf4j
@Service
public class ImageServiceImpl implements ImageService {
    private static final long EXPIRES_MILLS_50_YEARS = 50 * 365 * 24 * 3600 * 1000L;

    /** DAO */
    @Autowired
    private ImageDAO imageDAO;

    @Autowired
    private ImageCaptionUserDAO imageCaptionUserDAO;

    @Autowired
    private MaterialInfoDAO materialInfoDAO;

    @Autowired()
    @Qualifier("ossClientWithDataLabel")
    private OSS oss;

    @Override
    public ImageVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        ImageDO data = imageDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return ImageConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = imageDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除Image失败");
    }

    @Override
    public ImageVO insert(ImageVO image) {
        AssertUtil.assertNotNull(image, ResultCode.PARAM_INVALID, "image is null");
        AssertUtil.assertTrue(image.getId() == null, ResultCode.PARAM_INVALID, "image.id is present");

        // 创建时间、修改时间兜底
        if (image.getCreateTime() == null) {
            image.setCreateTime(new Date());
        }

        if (image.getModifyTime() == null) {
            image.setModifyTime(new Date());
        }

        ImageDO data = ImageConverter.vo2DO(image);
        // 逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        Integer n = imageDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建Image失败");
        AssertUtil.assertNotNull(data.getId(), "新建Image返回id为空");
        image.setId(data.getId());
        return image;
    }

    @Override
    public void updateByIdSelective(ImageVO image) {
        AssertUtil.assertNotNull(image, ResultCode.PARAM_INVALID, "image is null");
        AssertUtil.assertTrue(image.getId() != null, ResultCode.PARAM_INVALID, "image.id is null");

        // 修改时间必须更新
        image.setModifyTime(new Date());
        ImageDO data = ImageConverter.vo2DO(image);
        // 逻辑删除标过滤
        data.setDeleted(false);
        int n = imageDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新Image失败，影响行数:" + n);
    }

    @Override
    public void updateClothType(ImageQuery image) {
        ImageExample example = ImageConverter.query2Example(image);
        ImageExample.Criteria criteria = example.getOredCriteria().get(0);
        criteria.andTypeEqualTo("cloth");
        criteria.andUrlLike("https://aigc-platform%");
        criteria.andClothTypeNotExist();

        // 从数据库获取 ImageDO 对象列表
        List<ImageDO> images = imageDAO.selectByExample(example);

        // 处理 image 列表，提取 url 并解析
        images.forEach(img -> {
            try {
                // 使用 URI 类来安全地获取路径部分
                String path = new URI(img.getUrl()).getPath();
                // 路径示例: /202507/100134/dd8d6c3a6ccb496fa86f0fcad766ccdd.jpg
                String[] parts = path.split("/");
                // 分割后 parts[0]为空, parts[1]为日期, parts[2]为userId, parts[3]为带后缀的uuid
                if (parts.length >= 4) {
                    String userId = parts[2];
                    String filename = parts[3];
                    // 移除文件扩展名
                    int lastDotIndex = filename.lastIndexOf('.');
                    String uuid = (lastDotIndex == -1) ? filename : filename.substring(0, lastDotIndex);
                    MaterialInfoQuery materialInfoQuery = new MaterialInfoQuery();
                    materialInfoQuery.setPageNum(1);
                    materialInfoQuery.setPageSize(1);
                    try {
                        Integer.parseInt(userId);
                    } catch (NumberFormatException e) {
                        return;
                    }
                    materialInfoQuery.setUserId(Integer.parseInt(userId));
                    MaterialInfoExample materialInfoExample = MaterialInfoConverter.query2Example(
                            materialInfoQuery);
                    materialInfoExample.getOredCriteria().get(0).andMaterialDetailLike("%" + uuid + "%");
                    List<MaterialInfoDO> materialInfos = materialInfoDAO.selectByExample(materialInfoExample);
                    if (materialInfos.isEmpty()) {
                        // 通过 user_id 找不到的话，则直接通过 material_detail 找
                        materialInfoQuery.setUserId(null);
                        MaterialInfoExample example1 = MaterialInfoConverter.query2Example(materialInfoQuery);
                        example1.getOredCriteria().get(0).andMaterialDetailLike("%" + uuid + "%");
                        materialInfos = materialInfoDAO.selectByExample(example1);
                        if (materialInfos.isEmpty()) {
                            return;
                        }
                    }
                    String clothType = materialInfos.get(0).getSubType();
                    ClothTypeEnum clothTypeCode = ClothTypeEnum.getByCode(clothType);
                    if (clothTypeCode != null) {
                        String clothTypeDesc = clothTypeCode.getDesc();
                        JSONObject jsonObject = JSON.parseObject(img.getMetadata());
                        jsonObject.put("clothType", clothType);
                        jsonObject.put("clothTypeDesc", clothTypeDesc);
                        ImageDO imageUpdate = new ImageDO();
                        imageUpdate.setId(img.getId());
                        imageUpdate.setMetadata(jsonObject.toString());
                        imageUpdate.setModifyTime(new Date());
                        imageDAO.updateByPrimaryKeySelective(imageUpdate);
                    }
                }
            } catch (URISyntaxException e) {
                log.error("Invalid URL syntax: {}", img.getUrl(), e);
            }
        });
    }

    @Override
    public List<ImageVO> queryImageList(ImageQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ImageExample example = ImageConverter.query2Example(query);

        List<ImageDO> list = imageDAO.selectByExample(example);
        return ImageConverter.doList2VOList(list);
    }

    @Override
    public List<String> queryImageTags(ImageQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");
        ImageDO imageDO = new ImageDO();
        imageDO.setType(query.getType());
        return imageDAO.selectAllTags(imageDO);
    }

    @Override
    public Long queryImageCount(ImageQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ImageExample example = ImageConverter.query2Example(query);
        return imageDAO.countByExample(example);
    }

    /**
     * 带条件分页查询图像基本信息
     */
    @Override
    public PageInfo<ImageVO> queryImageByPage(ImageQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
                "pageNum or pageSize is invalid,pageNum:"
                        + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<ImageVO> page = new PageInfo<>();

        ImageExample example = ImageConverter.query2Example(query);
        long totalCount = imageDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<ImageDO> list = imageDAO.selectByExample(example);
        page.setList(ImageConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    /**
     * 查询总数
     */
    @Override
    public Map<String, Long> queryBatchCount(ImageQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ImageExample example = ImageConverter.query2Example(query);
        ImageBatchCountParam imageBatchCountParam = new ImageBatchCountParam();
        imageBatchCountParam.setExample(example);
        imageBatchCountParam.setFieldConfigs(query.getMetadataFieldConfigs());
        return imageDAO.batchCountByExample(imageBatchCountParam);
    }

    @Override
    public Map<String, Long> queryFieldValues(ImageQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ImageExample example = ImageConverter.query2Example(query);
        ImageBatchCountParam imageBatchCountParam = new ImageBatchCountParam();
        imageBatchCountParam.setExample(example);
        imageBatchCountParam.setFieldConfigs(query.getMetadataFieldConfigs());
        return imageDAO.selectDistinctFieldValues(imageBatchCountParam);
    }

    @Override
    public Integer saveClothInfo(ClothInfoVO clothInfo) {
        JSONObject metadata = new JSONObject();
        metadata.put("materialModelId", clothInfo.getId());
        metadata.put("source", "musegate-material-model");
        metadata.put("clothType", clothInfo.getClothType());
        metadata.put("clothTypeDesc", clothInfo.getClothTypeDesc());

        ImageQuery imageQuery = new ImageQuery();
        imageQuery.setUrl(clothInfo.getFullBodyFrontViewImageUrl());
        imageQuery.setType("cloth");
        List<ImageDO> images = imageDAO.selectByExample(ImageConverter.query2Example(imageQuery));
        if (!images.isEmpty()) {
            return images.getFirst().getId();
        }
        ImageDO imageDO = new ImageDO();
        imageDO.setUrl(clothInfo.getFullBodyFrontViewImageUrl());
        imageDO.setType("cloth");
        imageDO.setMetadata(metadata.toString());
        imageDAO.insertSelective(imageDO);
        return imageDO.getId();
    }

    @SneakyThrows
    @Override
    public void updateImageHash(ImageQuery query) {
        List<ImageDO> images = imageDAO.selectByExample(ImageConverter.query2Example(query));

        // 1. 创建 HttpClient
        try (HttpClient client = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(10))
            .build()) {

            // 2. 使用虚拟线程执行器
            try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {

                // 3. 信号量控制最大并发：最多 50 个 HTTP 请求同时进行
                Semaphore semaphore = new Semaphore(50);

                List<CompletableFuture<Void>> futures = new ArrayList<>();

                for (ImageDO image : images) {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        boolean acquired = false;
                        try {
                            // 获取许可（最多 50 个并发）
                            semaphore.acquire();
                            acquired = true;

                            HttpRequest request = HttpRequest.newBuilder()
                                .uri(URI.create(image.getUrl()))
                                .timeout(Duration.ofSeconds(30))
                                .GET()
                                .build();

                            // 发送请求（阻塞操作，但虚拟线程不会阻塞平台线程）
                            HttpResponse<byte[]> response = client.send(request, HttpResponse.BodyHandlers.ofByteArray());

                            byte[] body = response.body();

                            // 计算 MD5
                            String contentMd5 = DigestUtils.md5Hex(body).toUpperCase();

                            // 更新数据库
                            ImageDO imageDO = new ImageDO();
                            imageDO.setId(image.getId());
                            imageDO.setImageHash(contentMd5);
                            imageDO.setMetadata(getImageMetadata(body).toString());
                            imageDAO.updateByPrimaryKeySelective(imageDO);

                        } catch (Exception e) {
                            throw new RuntimeException("Failed to process image: " + image.getUrl(), e);
                        } finally {
                            if (acquired) {
                                semaphore.release(); // 确保释放许可
                            }
                        }
                    }, executor);

                    futures.add(future);
                }

                // 4. 等待所有任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            } catch (Exception e) {
                throw new RuntimeException("Error during virtual thread execution", e);
            }
            // executor 自动关闭（所有任务完成后）
        } // HttpClient 安全关闭（所有请求已完成）
    }

    @SneakyThrows
    @Override
    public ImageVO upload(ImageUploadReq req) {
        // 1. 参数校验
        ImageTypeEnum typeEnum = ImageTypeEnum.getByCode(req.getType());
        AssertUtil.assertNotNull(typeEnum, "不支持的 imageType 类型");

        // 2. 解码并获取 imageBase64 和 hash（提前处理，避免重复）
        byte[] imageBytes = null;
        String imageHash = null;
        if (StringUtils.isNotBlank(req.getImageBase64())) {
            imageBytes = decodeBase64(req.getImageBase64());
            imageHash = DigestUtils.md5Hex(imageBytes).toUpperCase();
        }

        // 场景1：根据 imagePath 查找是否存在
        ImageQuery pathQuery = new ImageQuery();
        pathQuery.setImagePath(req.getImagePath());
        pathQuery.setType(req.getType());
        List<ImageVO> imagesByPath = queryImageList(pathQuery);
        if (!imagesByPath.isEmpty()) {
            ImageVO existing = imagesByPath.getFirst();
            return handleExistingImage(existing, req, imageBytes, imageHash, req.isNeedUpdateBase64());
        }

        // 如果没有 imageBase64，则无法上传新图
        AssertUtil.assertNotBlank(req.getImageBase64(), ResultCode.PARAM_INVALID, "imageBase64 为空");

        // 场景2：根据 imageHash 查找是否存在
        ImageQuery hashQuery = new ImageQuery();
        hashQuery.setImageHash(imageHash);
        hashQuery.setType(req.getType());
        List<ImageVO> imagesByHash = queryImageList(hashQuery);
        if (!imagesByHash.isEmpty()) {
            ImageVO existing = imagesByHash.getFirst();
            return handleExistingImage(existing, req, null, null, req.isNeedUpdateBase64()); // 已存在，无需再传图
        }

        // 场景3：全新图片，上传并插入
        return createNewImage(req, imageBytes, imageHash);
    }

    private ImageVO handleExistingImage(ImageVO existing, ImageUploadReq req, byte[] imageBytes, String imageHash,
                                        boolean needUpdateBase64) {
        ImageVO update = new ImageVO();
        update.setId(existing.getId());
        update.setMetadata(JsonMerger.merge(existing.getMetadata(), req.getMetadata()));
        update.setModifyTime(new Date());

        boolean needUpload = false;
        String newUrl = null;

        // 如果提供了新的 base64 图片，且 hash 不同，则需要替换
        if (needUpdateBase64 && imageBytes != null && !Arrays.equals(imageHash.getBytes(), existing.getImageHash().getBytes())) {
            needUpload = true;
            newUrl = upload(getOssKey(req.getImagePath()), new ByteArrayInputStream(imageBytes));
        }

        if (needUpload) {
            update.setImageHash(imageHash);
            update.setUrl(newUrl);
            existing.setImageHash(imageHash);
            existing.setUrl(newUrl);
        }

        updateByIdSelective(update);

        // 更新返回对象
        existing.setMetadata(update.getMetadata());
        existing.setModifyTime(update.getModifyTime());
        return existing;
    }

    private ImageVO createNewImage(ImageUploadReq req, byte[] imageBytes, String imageHash) {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(imageBytes);
        String url = upload(getOssKey(req.getImagePath()), inputStream);

        ImageVO imageVO = new ImageVO();
        imageVO.setType(req.getType());
        imageVO.setImageHash(imageHash);
        imageVO.setImagePath(req.getImagePath());
        imageVO.setUrl(url);
        imageVO.setMetadata(req.getMetadata());
        return insert(imageVO);
    }

    private byte[] decodeBase64(String imageBase64) {
        String[] split = imageBase64.split(",", 2);
        AssertUtil.assertTrue(split.length > 1, "imageBase64 format error");
        return Base64.getDecoder().decode(split[1]);
    }

    private JSONObject getImageMetadata(byte[] bytes) {
        ImageInfo imageInfo = ImageInfoUtil.getImageInfo(bytes);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("image_size", imageInfo.size());
        jsonObject.put("image_width", imageInfo.width());
        jsonObject.put("image_height", imageInfo.height());
        return jsonObject;
    }

    /**
     * 上传文件流到oss
     * 
     * @param ossKey
     * @param is
     * @return
     */
    private String upload(String ossKey, InputStream is) {
        if (StringUtils.isEmpty(ossKey) || is == null) {
            throw new IllegalArgumentException("ossKey is empty or input stream is null");
        }

        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest("mg-data-label", ossKey, is);
            PutObjectResult result = oss.putObject(putObjectRequest);
            log.info("upload file to oss, ossKey={} , response:{}", ossKey,
                    com.alibaba.fastjson.JSONObject.toJSONString(result));

            // 设置签名URL过期时间，单位为毫秒，50年
            String url = getSignedFileUrl(ossKey);
            if (url != null)
                return url;

        } catch (Throwable e) {
            log.error("upload file to oss failed:" + ossKey, e);
        }

        return null;
    }

    private String getOssKey(String imagePath) {
        // 生成唯一的 OSS key
        long timestamp = System.currentTimeMillis();
        String uuid = UUID.randomUUID().toString().replace("-", "");

        // 从 imagePath 中提取扩展名
        String extension = "";
        if (StringUtils.isNotBlank(imagePath)) {
            int lastDotIndex = imagePath.lastIndexOf('.');
            if (lastDotIndex > 0 && lastDotIndex < imagePath.length() - 1) {
                extension = imagePath.substring(lastDotIndex + 1).toLowerCase();
            }
        }

        // 构建 OSS key: images/年月日/时间戳_uuid.扩展名
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return String.format("images/%s/%d_%s.%s", dateStr, timestamp, uuid, extension);
    }

    private String getSignedFileUrl(String ossKey) {
        if (StringUtils.isBlank(ossKey)) {
            throw new IllegalArgumentException("ossKey is blank");
        }

        // 设置签名URL过期时间，单位为毫秒，50年
        Date expiration = new Date(new Date().getTime() + EXPIRES_MILLS_50_YEARS);

        // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
        URL url = oss.generatePresignedUrl("mg-data-label", ossKey, expiration, HttpMethod.GET);
        return url.toExternalForm();
    }

    @Override
    public Map<String, Integer> countByIntendedUse() {
        List<Map<String, Object>> results = imageDAO.countByIntendedUse();
        Map<String, Integer> countMap = new HashMap<>();

        for (Map<String, Object> result : results) {
            String intendedUse = (String) result.get("intendedUse");
            Long count = (Long) result.get("count");
            countMap.put(intendedUse, count.intValue());
        }

        return countMap;
    }

    @Override
    public List<ImageVO> selectUncaptionedSceneImages4Embedding(Integer limit) {
        List<ImageDO> imageDOS = imageDAO.selectUncaptionedSceneImages4Embedding(limit);
        if (imageDOS == null) {
            return null;
        }
        return ImageConverter.doList2VOList(imageDOS);
    }


    @Override
    public List<ImageVO> selectGeminiFlashCaptionedSceneImages4Embedding(Integer limit) {
        List<ImageDO> imageDOS = imageDAO.selectGeminiFlashCaptionedSceneImages4Embedding(limit);
        if (imageDOS == null) {
            return null;
        }
        return ImageConverter.doList2VOList(imageDOS);
    }

    @Override
    public ImageVO getByHashOrPath(String hash, String path) {
        AssertUtil.assertTrue(StringUtils.isNotBlank(hash) || StringUtils.isNotBlank(path), "hash and path is null");
        ImageDO img = imageDAO.getByHashOrPath(hash, path);
        if (img == null) {
            return null;
        }
        return ImageConverter.do2VO(img);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTryonById(ImageVO image) {
        ImageVO existingImage = selectById(image.getId());
        ImageVO imageUpdate = new ImageVO();
        imageUpdate.setId(image.getId());
        imageUpdate.setMetadata(JsonMerger.merge(existingImage.getMetadata(), image.getMetadata()));
        updateByIdSelective(imageUpdate);
    }
}